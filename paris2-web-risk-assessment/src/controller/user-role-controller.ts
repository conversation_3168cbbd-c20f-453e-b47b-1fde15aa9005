import {JwtUser} from '../types/user';
import {RISK_ASSESMENT_ROLES as ROLES} from '../constants/roles';

class UserRoleController {
  getConfig(kc: any) {
    const hasRole = (role: string): boolean =>
      kc.realmAccess.roles.includes(role);

    return {
      user: kc.tokenParsed as JwtUser,
      riskAssessment: {
        canViewTemplate: hasRole(ROLES.HAS_TEMPLATE_VIEW),
        canAddTemplate: hasRole(ROLES.HAS_TEMPLATE_ADD),
        canArchiveTemplate: hasRole(ROLES.HAS_TEMPLATE_ARCHIVE),
        canViewDraftTemplate: hasRole(ROLES.HAS_TEMPLATE_DRAFT_VIEW),
        canEditDraftTemplate: hasRole(ROLES.HAS_TEMPLATE_DRAFT_EDIT),
        canDiscardDraftTemplate: hasRole(ROLES.HAS_TEMPLATE_DRAFT_DISCARD),
        canViewRA: hasRole(ROLES.HAS_RA_VIEW),
        canViewDraftRA: hasRole(ROLES.HAS_RA_DRAFT_VIEW),
        canEditDraftRA: hasRole(ROLES.HAS_RA_DRAFT_EDIT),
        canDiscardDraftRA: hasRole(ROLES.HAS_RA_DRAFT_DISCARD),
        canViewMasterRA: hasRole(ROLES.HAS_RA_MASTER_VIEW),
        canAddRA: hasRole(ROLES.HAS_RA_ADD),
        canExportPDF: hasRole(ROLES.HAS_RA_EXPORT),
        canAddRALvl1: hasRole(ROLES.HAS_RA_R1_ADD),
        canApproveRisk: hasRole(ROLES.HAS_RA_APPROVE),
        hasAdminAccess: hasRole(ROLES.HAS_RA_ADMIN),
        hasPermision:
          hasRole(ROLES.HAS_TEMPLATE_VIEW) ||
          hasRole(ROLES.HAS_RA_VIEW) ||
          hasRole(ROLES.HAS_RA_MASTER_VIEW) ||
          hasRole(ROLES.HAS_RA_ADMIN),
        canViewDrafts:
          hasRole(ROLES.HAS_RA_DRAFT_VIEW) ||
          hasRole(ROLES.HAS_TEMPLATE_DRAFT_VIEW),
        canCreateNew:
          hasRole(ROLES.HAS_RA_ADD) || hasRole(ROLES.HAS_TEMPLATE_ADD),
        canCreateNewTemplate: hasRole(ROLES.HAS_TEMPLATE_VIEW),
      },
    };
  }
}

export default UserRoleController;
