import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import {MemoryRouter} from 'react-router-dom';
import '@testing-library/jest-dom';
import {RADraftHeader} from '../../../src/pages/RADrafts/RADraftHeader';

// Mock the useDataStoreContext hook
jest.mock('../../../src/context/DataStoreProvider', () => ({
  useDataStoreContext: () => ({
    roleConfig: {
      user: { user_id: 'test-user' },
      riskAssessment: {
        hasPermision: true,
        canCreateNew: true,
        canViewDraftRA: true,
        canViewDraftTemplate: true,
      },
    },
  }),
}));

describe('RADraftHeader', () => {
  const mockSetActiveTab = jest.fn();

  const defaultProps = {
    activeTab: 1,
    setActiveTab: mockSetActiveTab,
  };

  const renderWithRouter = (component: React.ReactElement) => {
    return render(component, {wrapper: MemoryRouter});
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders the component without crashing', () => {
      renderWithRouter(<RADraftHeader {...defaultProps} />);

      expect(
        screen.getByRole('link', {name: 'Risk Assessment'}),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', {name: 'Risk Assessment'}),
      ).toBeInTheDocument();
      expect(screen.getByText('Drafts')).toBeInTheDocument();
    });

    it('renders breadcrumb navigation correctly', () => {
      renderWithRouter(<RADraftHeader {...defaultProps} />);

      const riskAssessmentLink = screen.getByRole('link', {
        name: 'Risk Assessment',
      });
      expect(riskAssessmentLink).toBeInTheDocument();
      expect(riskAssessmentLink).toHaveAttribute('href', '/risk-assessment');
      expect(riskAssessmentLink).toHaveClass(
        'underline',
        'fs-24',
        'secondary-color',
      );

      const draftsText = screen.getByText('Drafts');
      expect(draftsText).toBeInTheDocument();
      expect(draftsText).toHaveClass('fs-24', 'secondary-color');
    });

    it('renders tab buttons correctly', () => {
      renderWithRouter(<RADraftHeader {...defaultProps} />);

      const riskAssessmentTab = screen.getByRole('button', {
        name: 'Risk Assessment',
      });
      const raTemplateTab = screen.getByRole('button', {name: 'RA Template'});

      expect(riskAssessmentTab).toBeInTheDocument();
      expect(raTemplateTab).toBeInTheDocument();
    });

    it('applies correct container classes', () => {
      const {container} = renderWithRouter(<RADraftHeader {...defaultProps} />);

      const mainContainer = container.firstChild as HTMLElement;
      expect(mainContainer).toHaveClass(
        'd-flex',
        'justify-content-between',
        'align-items-center',
        'mb-4',
      );
    });

    it('applies correct breadcrumb container classes', () => {
      renderWithRouter(<RADraftHeader {...defaultProps} />);

      const breadcrumbContainer = document.querySelector(
        '.fs-24.secondary-color',
      );
      expect(breadcrumbContainer).toBeInTheDocument();
    });

    it('applies correct tab container classes', () => {
      renderWithRouter(<RADraftHeader {...defaultProps} />);

      const tabContainer = document.querySelector(
        '.d-flex.border.rounded.overflow-hidden',
      );
      expect(tabContainer).toBeInTheDocument();
    });
  });

  describe('Tab Functionality', () => {
    it('shows Risk Assessment tab as active when activeTab is 1', () => {
      renderWithRouter(<RADraftHeader {...defaultProps} activeTab={1} />);

      const riskAssessmentTab = screen.getByRole('button', {
        name: 'Risk Assessment',
      });
      const raTemplateTab = screen.getByRole('button', {name: 'RA Template'});

      expect(riskAssessmentTab).toHaveClass('draft-listing-tab', 'active-tab');
      expect(raTemplateTab).toHaveClass('draft-listing-tab', 'inactive-tab');
    });

    it('shows RA Template tab as active when activeTab is 2', () => {
      renderWithRouter(<RADraftHeader {...defaultProps} activeTab={2} />);

      const riskAssessmentTab = screen.getByRole('button', {
        name: 'Risk Assessment',
      });
      const raTemplateTab = screen.getByRole('button', {name: 'RA Template'});

      expect(riskAssessmentTab).toHaveClass(
        'draft-listing-tab',
        'inactive-tab',
      );
      expect(raTemplateTab).toHaveClass('draft-listing-tab', 'active-tab');
    });

    it('calls setActiveTab with 1 when Risk Assessment tab is clicked', () => {
      renderWithRouter(<RADraftHeader {...defaultProps} activeTab={2} />);

      const riskAssessmentTab = screen.getByRole('button', {
        name: 'Risk Assessment',
      });
      fireEvent.click(riskAssessmentTab);

      expect(mockSetActiveTab).toHaveBeenCalledTimes(1);
      expect(mockSetActiveTab).toHaveBeenCalledWith(1);
    });

    it('calls setActiveTab with 2 when RA Template tab is clicked', () => {
      renderWithRouter(<RADraftHeader {...defaultProps} activeTab={1} />);

      const raTemplateTab = screen.getByRole('button', {name: 'RA Template'});
      fireEvent.click(raTemplateTab);

      expect(mockSetActiveTab).toHaveBeenCalledTimes(1);
      expect(mockSetActiveTab).toHaveBeenCalledWith(2);
    });

    it('handles multiple tab clicks correctly', () => {
      renderWithRouter(<RADraftHeader {...defaultProps} activeTab={1} />);

      const riskAssessmentTab = screen.getByRole('button', {
        name: 'Risk Assessment',
      });
      const raTemplateTab = screen.getByRole('button', {name: 'RA Template'});

      // Click RA Template tab
      fireEvent.click(raTemplateTab);
      expect(mockSetActiveTab).toHaveBeenCalledWith(2);

      // Click Risk Assessment tab
      fireEvent.click(riskAssessmentTab);
      expect(mockSetActiveTab).toHaveBeenCalledWith(1);

      expect(mockSetActiveTab).toHaveBeenCalledTimes(2);
    });
  });

  describe('Button Properties', () => {
    it('applies correct variant to tab buttons', () => {
      renderWithRouter(<RADraftHeader {...defaultProps} />);

      const riskAssessmentTab = screen.getByRole('button', {
        name: 'Risk Assessment',
      });
      const raTemplateTab = screen.getByRole('button', {name: 'RA Template'});

      expect(riskAssessmentTab).toHaveClass('btn-light');
      expect(raTemplateTab).toHaveClass('btn-light');
    });

    it('applies draft-listing-tab class to both buttons', () => {
      renderWithRouter(<RADraftHeader {...defaultProps} />);

      const riskAssessmentTab = screen.getByRole('button', {
        name: 'Risk Assessment',
      });
      const raTemplateTab = screen.getByRole('button', {name: 'RA Template'});

      expect(riskAssessmentTab).toHaveClass('draft-listing-tab');
      expect(raTemplateTab).toHaveClass('draft-listing-tab');
    });
  });

  describe('Edge Cases', () => {
    it('handles activeTab value of 0 correctly', () => {
      renderWithRouter(<RADraftHeader {...defaultProps} activeTab={0} />);

      const riskAssessmentTab = screen.getByRole('button', {
        name: 'Risk Assessment',
      });
      const raTemplateTab = screen.getByRole('button', {name: 'RA Template'});

      expect(riskAssessmentTab).toHaveClass('inactive-tab');
      expect(raTemplateTab).toHaveClass('inactive-tab');
    });

    it('handles activeTab value greater than 2 correctly', () => {
      renderWithRouter(<RADraftHeader {...defaultProps} activeTab={3} />);

      const riskAssessmentTab = screen.getByRole('button', {
        name: 'Risk Assessment',
      });
      const raTemplateTab = screen.getByRole('button', {name: 'RA Template'});

      expect(riskAssessmentTab).toHaveClass('inactive-tab');
      expect(raTemplateTab).toHaveClass('inactive-tab');
    });

    it('handles negative activeTab value correctly', () => {
      renderWithRouter(<RADraftHeader {...defaultProps} activeTab={-1} />);

      const riskAssessmentTab = screen.getByRole('button', {
        name: 'Risk Assessment',
      });
      const raTemplateTab = screen.getByRole('button', {name: 'RA Template'});

      expect(riskAssessmentTab).toHaveClass('inactive-tab');
      expect(raTemplateTab).toHaveClass('inactive-tab');
    });
  });

  describe('Accessibility', () => {
    it('has proper button roles', () => {
      renderWithRouter(<RADraftHeader {...defaultProps} />);

      const riskAssessmentTab = screen.getByRole('button', {
        name: 'Risk Assessment',
      });
      const raTemplateTab = screen.getByRole('button', {name: 'RA Template'});

      expect(riskAssessmentTab.tagName).toBe('BUTTON');
      expect(raTemplateTab.tagName).toBe('BUTTON');
    });

    it('has proper link role for breadcrumb', () => {
      renderWithRouter(<RADraftHeader {...defaultProps} />);

      const riskAssessmentLink = screen.getByRole('link', {
        name: 'Risk Assessment',
      });
      expect(riskAssessmentLink.tagName).toBe('A');
    });
  });

  describe('Component Structure', () => {
    it('maintains proper DOM hierarchy', () => {
      const {container} = renderWithRouter(<RADraftHeader {...defaultProps} />);

      const mainContainer = container.firstChild as HTMLElement;
      const breadcrumbSection = mainContainer.children[0];
      const tabSection = mainContainer.children[1];

      expect(mainContainer.children).toHaveLength(2);
      expect(breadcrumbSection).toHaveClass('fs-24', 'secondary-color');
      expect(tabSection).toHaveClass(
        'd-flex',
        'border',
        'rounded',
        'overflow-hidden',
      );
    });

    it('contains both tabs within tab container', () => {
      renderWithRouter(<RADraftHeader {...defaultProps} />);

      const tabContainer = document.querySelector(
        '.d-flex.border.rounded.overflow-hidden',
      );
      const riskAssessmentTab = screen.getByRole('button', {
        name: 'Risk Assessment',
      });
      const raTemplateTab = screen.getByRole('button', {name: 'RA Template'});

      expect(tabContainer).toContainElement(riskAssessmentTab);
      expect(tabContainer).toContainElement(raTemplateTab);
    });
  });

  describe('Props Validation', () => {
    it('works with different setActiveTab functions', () => {
      const alternativeSetActiveTab = jest.fn();

      renderWithRouter(
        <RADraftHeader activeTab={1} setActiveTab={alternativeSetActiveTab} />,
      );

      const raTemplateTab = screen.getByRole('button', {name: 'RA Template'});
      fireEvent.click(raTemplateTab);

      expect(alternativeSetActiveTab).toHaveBeenCalledWith(2);
      expect(mockSetActiveTab).not.toHaveBeenCalled();
    });
  });
});
