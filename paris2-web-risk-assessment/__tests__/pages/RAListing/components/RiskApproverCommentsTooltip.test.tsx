import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import userEvent from '@testing-library/user-event';
import RiskApproverCommentsTooltip from '../../../../src/pages/RAListing/components/RiskApproverCommentsTooltip';
import { RAItemFull } from '../../../../src/types';

// Mock dependencies
jest.mock('../../../../src/pages/CreateRA/AddApproverCard', () => ({
  getApproverStatusText: jest.fn((approver) => {
    if (approver?.status === 1 && approver?.approval_status === 3) {
      return ['Approved with Condition', 'green'];
    }
    if (approver?.status === 1 && approver?.approval_status === 1) {
      return ['Approved', 'green'];
    }
    if (approver?.status === 1 && approver?.approval_status === 2) {
      return ['Rejected', 'red'];
    }
    return ['Pending', 'yellow'];
  }),
}));

jest.mock('../../../../src/components/ColoredTile', () => ({
  __esModule: true,
  default: ({ text, theme }: { text: string; theme: string }) => (
    <div data-testid="colored-tile" data-theme={theme}>
      {text}
    </div>
  ),
}));

jest.mock('../../../../src/utils/svgIcons', () => ({
  CommentIcon: (props: any) => (
    <svg data-testid="comment-icon" {...props}>
      <path d="mock-comment-icon" />
    </svg>
  ),
}));

describe('RiskApproverCommentsTooltip', () => {
  const createMockApprover = (overrides: Partial<RAItemFull['risk_approver'][0]> = {}): RAItemFull['risk_approver'][0] => ({
    id: 1,
    risk_id: 1,
    keycloak_id: 'user-123',
    user_name: 'John Doe',
    user_email: btoa('<EMAIL>'),
    job_title: 'Senior Engineer',
    message: null,
    approval_order: 1,
    approval_status: null,
    approval_date: null,
    status: 1,
    ...overrides,
  });

  describe('Empty state', () => {
    it('renders empty state when no approvers have status', () => {
      const riskApprovers: RAItemFull['risk_approver'] = [
        createMockApprover({ status: 0 }),
        createMockApprover({ id: 2, status: 0 }),
      ];

      render(<RiskApproverCommentsTooltip riskApprovers={riskApprovers} />);

      expect(screen.getByTestId('comment-icon')).toBeInTheDocument();
      expect(screen.getByText('-')).toBeInTheDocument();
      
      const container = screen.getByText('-').parentElement;
      expect(container).toHaveStyle({
        cursor: 'default',
        display: 'inline-flex',
        alignItems: 'center',
      });
    });

    it('renders empty state when approvers array is empty', () => {
      render(<RiskApproverCommentsTooltip riskApprovers={[]} />);

      expect(screen.getByTestId('comment-icon')).toBeInTheDocument();
      expect(screen.getByText('-')).toBeInTheDocument();
    });

    it('handles null/undefined sortedApprovers edge case', () => {
      // This test covers the fallback case on line 23: sortedApprovers?.length ?? 0
      const riskApprovers: RAItemFull['risk_approver'] = [];

      render(<RiskApproverCommentsTooltip riskApprovers={riskApprovers} />);

      expect(screen.getByTestId('comment-icon')).toBeInTheDocument();
      expect(screen.getByText('-')).toBeInTheDocument();
    });


  });

  describe('Tooltip with approvers', () => {
    it('renders tooltip with single approver', async () => {
      const user = userEvent.setup();
      const riskApprovers: RAItemFull['risk_approver'] = [
        createMockApprover({
          user_name: 'Jane Smith',
          job_title: 'Manager',
          user_email: btoa('<EMAIL>'),
          status: 1,
          approval_status: 1,
        }),
      ];

      render(<RiskApproverCommentsTooltip riskApprovers={riskApprovers} />);

      expect(screen.getByTestId('comment-icon')).toBeInTheDocument();
      expect(screen.getByText('1')).toBeInTheDocument();

      const triggerElement = screen.getByText('1').parentElement;
      expect(triggerElement).toHaveStyle({
        cursor: 'pointer',
        display: 'inline-flex',
        alignItems: 'center',
      });

      await user.hover(triggerElement!);

      // Check tooltip content
      expect(await screen.findByText('Approver')).toBeInTheDocument(); // Single approver shows "Approver"
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.getByTestId('colored-tile')).toHaveTextContent('Approved');
      expect(screen.getByText('Manager • <EMAIL>')).toBeInTheDocument();
    });

    it('renders tooltip with multiple approvers and proper titles', async () => {
      const user = userEvent.setup();
      const riskApprovers: RAItemFull['risk_approver'] = [
        createMockApprover({
          id: 1,
          user_name: 'First User',
          approval_order: 1,
          status: 1,
          approval_status: 1,
        }),
        createMockApprover({
          id: 2,
          user_name: 'Second User',
          approval_order: 2,
          status: 1,
          approval_status: 3,
          message: 'Some condition',
        }),
        createMockApprover({
          id: 3,
          user_name: 'Third User',
          approval_order: 3,
          status: 1,
          approval_status: 2,
        }),
      ];

      render(<RiskApproverCommentsTooltip riskApprovers={riskApprovers} />);

      const triggerElement = screen.getByText('3').parentElement;
      await user.hover(triggerElement!);

      // Check reviewer titles
      expect(await screen.findByText('First Approver')).toBeInTheDocument();
      expect(screen.getByText('Second Approver')).toBeInTheDocument();
      expect(screen.getByText('Final Approver')).toBeInTheDocument();

      // Check names
      expect(screen.getByText('First User')).toBeInTheDocument();
      expect(screen.getByText('Second User')).toBeInTheDocument();
      expect(screen.getByText('Third User')).toBeInTheDocument();
    });

    it('handles approvers with null approval_order (sorting edge case)', async () => {
      const user = userEvent.setup();
      const riskApprovers: RAItemFull['risk_approver'] = [
        createMockApprover({
          id: 1,
          user_name: 'User With Order',
          approval_order: 2,
          status: 1,
        }),
        createMockApprover({
          id: 2,
          user_name: 'User Without Order',
          approval_order: null,
          status: 1,
        }),
        createMockApprover({
          id: 3,
          user_name: 'Another User With Order',
          approval_order: 1,
          status: 1,
        }),
      ];

      render(<RiskApproverCommentsTooltip riskApprovers={riskApprovers} />);

      const triggerElement = screen.getByText('3').parentElement;
      await user.hover(triggerElement!);

      // Should be sorted by approval_order, with null values last
      const approverElements = await screen.findAllByText(/User/);
      expect(approverElements[0]).toHaveTextContent('Another User With Order'); // order 1
      expect(approverElements[1]).toHaveTextContent('User With Order'); // order 2
      expect(approverElements[2]).toHaveTextContent('User Without Order'); // null order
    });

    it('renders approver with missing user_name as dash', async () => {
      const user = userEvent.setup();
      const riskApprovers: RAItemFull['risk_approver'] = [
        createMockApprover({
          user_name: '',
          status: 1,
        }),
      ];

      render(<RiskApproverCommentsTooltip riskApprovers={riskApprovers} />);

      const triggerElement = screen.getByText('1').parentElement;
      await user.hover(triggerElement!);

      expect(await screen.findByText('-')).toBeInTheDocument(); // name fallback
    });

    it('handles approver with no job_title and no email', async () => {
      const user = userEvent.setup();
      const riskApprovers: RAItemFull['risk_approver'] = [
        createMockApprover({
          user_name: 'John Doe',
          job_title: null,
          user_email: '',
          status: 1,
        }),
      ];

      render(<RiskApproverCommentsTooltip riskApprovers={riskApprovers} />);

      const triggerElement = screen.getByText('1').parentElement;
      await user.hover(triggerElement!);

      expect(await screen.findByText('John Doe')).toBeInTheDocument();
      // Should not show meta section when both job and email are empty
      expect(screen.queryByText('•')).not.toBeInTheDocument();
    });

    it('handles approver with only job_title', async () => {
      const user = userEvent.setup();
      const riskApprovers: RAItemFull['risk_approver'] = [
        createMockApprover({
          user_name: 'John Doe',
          job_title: 'Manager',
          user_email: '',
          status: 1,
        }),
      ];

      render(<RiskApproverCommentsTooltip riskApprovers={riskApprovers} />);

      const triggerElement = screen.getByText('1').parentElement;
      await user.hover(triggerElement!);

      expect(await screen.findByText('Manager •')).toBeInTheDocument();
    });

    it('handles approver with only email', async () => {
      const user = userEvent.setup();
      const riskApprovers: RAItemFull['risk_approver'] = [
        createMockApprover({
          user_name: 'John Doe',
          job_title: '',
          user_email: btoa('<EMAIL>'),
          status: 1,
        }),
      ];

      render(<RiskApproverCommentsTooltip riskApprovers={riskApprovers} />);

      const triggerElement = screen.getByText('1').parentElement;
      await user.hover(triggerElement!);

      expect(await screen.findByText((content, element) => {
        return element?.textContent === ' • <EMAIL>';
      })).toBeInTheDocument();
    });

    it('renders message for rejected approver', async () => {
      const user = userEvent.setup();
      const riskApprovers: RAItemFull['risk_approver'] = [
        createMockApprover({
          user_name: 'Rejector',
          approval_status: 2,
          message: 'This is not acceptable',
          status: 1,
        }),
      ];

      render(<RiskApproverCommentsTooltip riskApprovers={riskApprovers} />);

      const triggerElement = screen.getByText('1').parentElement;
      await user.hover(triggerElement!);

      expect(await screen.findByText('Reason for Rejection')).toBeInTheDocument();
      expect(screen.getByText('This is not acceptable')).toBeInTheDocument();
    });

    it('renders message for approved with condition', async () => {
      const user = userEvent.setup();
      const riskApprovers: RAItemFull['risk_approver'] = [
        createMockApprover({
          user_name: 'Conditional Approver',
          approval_status: 3,
          message: '  Some condition with spaces  ',
          status: 1,
        }),
      ];

      render(<RiskApproverCommentsTooltip riskApprovers={riskApprovers} />);

      const triggerElement = screen.getByText('1').parentElement;
      await user.hover(triggerElement!);

      expect(await screen.findByText('Condition for Approval')).toBeInTheDocument();
      expect(screen.getByText('Some condition with spaces')).toBeInTheDocument(); // trimmed
    });

    it('renders divider between multiple approvers but not after last one', async () => {
      const user = userEvent.setup();
      const riskApprovers: RAItemFull['risk_approver'] = [
        createMockApprover({
          id: 1,
          user_name: 'First User',
          approval_order: 1,
          status: 1,
        }),
        createMockApprover({
          id: 2,
          user_name: 'Second User',
          approval_order: 2,
          status: 1,
        }),
      ];

      render(<RiskApproverCommentsTooltip riskApprovers={riskApprovers} />);

      const triggerElement = screen.getByText('2').parentElement;
      await user.hover(triggerElement!);

      await screen.findByText('First User');

      // Should have one hr element (divider between first and second)
      const dividers = document.querySelectorAll('hr');
      expect(dividers).toHaveLength(1);
    });

    it('handles more than 3 approvers with custom titles', async () => {
      const user = userEvent.setup();
      const riskApprovers: RAItemFull['risk_approver'] = [
        createMockApprover({ id: 1, user_name: 'User 1', approval_order: 1, status: 1 }),
        createMockApprover({ id: 2, user_name: 'User 2', approval_order: 2, status: 1 }),
        createMockApprover({ id: 3, user_name: 'User 3', approval_order: 3, status: 1 }),
        createMockApprover({ id: 4, user_name: 'User 4', approval_order: 4, status: 1 }),
      ];

      render(<RiskApproverCommentsTooltip riskApprovers={riskApprovers} />);

      const triggerElement = screen.getByText('4').parentElement;
      await user.hover(triggerElement!);

      expect(await screen.findByText('First Approver')).toBeInTheDocument();
      expect(screen.getByText('Second Approver')).toBeInTheDocument();
      expect(screen.getByText('Final Approver')).toBeInTheDocument();
      expect(screen.getByText('Approver 4')).toBeInTheDocument(); // Custom title for 4th
    });
  });
});
