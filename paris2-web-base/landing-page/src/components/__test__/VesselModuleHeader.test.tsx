import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { VesselModuleHeader } from '../dashboard/VesselWidget/VesselModuleHeader';

// Mock the lucide-react icons
jest.mock('lucide-react', () => ({
  LayoutGrid: ({ className, onClick }: any) => (
    <div data-testid="layout-grid-icon" className={className} onClick={onClick}>
      LayoutGrid
    </div>
  ),
  List: ({ className, onClick }: any) => (
    <div data-testid="list-icon" className={className} onClick={onClick}>
      List
    </div>
  ),
  Minimize2: ({ className, onClick }: any) => (
    <div data-testid="minimize-icon" className={className} onClick={onClick}>
      Minimize2
    </div>
  ),
}));

// Mock the EnlargeIcon
jest.mock('../dashboard/VesselWidget/svgIcons', () => ({
  EnlargeIcon: ({ className, onClick }: any) => (
    <div data-testid="enlarge-icon" className={className} onClick={onClick}>
      EnlargeIcon
    </div>
  ),
}));

describe('VesselModuleHeader', () => {
  const defaultProps = {
    title: 'Test Vessel Module',
    viewMode: 'list' as const,
    isModal: false,
    IsiconRenderVisible: true,
    IsenLargeIconVisible: true,
    onViewModeChange: jest.fn(),
    onToggleModal: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render title correctly', () => {
    render(<VesselModuleHeader {...defaultProps} />);
    
    expect(screen.getByText('Test Vessel Module')).toBeInTheDocument();
  });

  it('should render view mode icons when IsiconRenderVisible is true', () => {
    render(<VesselModuleHeader {...defaultProps} IsiconRenderVisible={true} />);
    
    expect(screen.getByTestId('list-icon')).toBeInTheDocument();
    expect(screen.getByTestId('layout-grid-icon')).toBeInTheDocument();
  });

  it('should not render view mode icons when IsiconRenderVisible is false', () => {
    render(<VesselModuleHeader {...defaultProps} IsiconRenderVisible={false} />);
    
    expect(screen.queryByTestId('list-icon')).not.toBeInTheDocument();
    expect(screen.queryByTestId('layout-grid-icon')).not.toBeInTheDocument();
  });

  it('should render enlarge icon when IsenLargeIconVisible is true and not in modal', () => {
    render(
      <VesselModuleHeader 
        {...defaultProps} 
        IsenLargeIconVisible={true} 
        isModal={false} 
      />
    );
    
    expect(screen.getByTestId('enlarge-icon')).toBeInTheDocument();
  });

  it('should render minimize icon when in modal', () => {
    render(
      <VesselModuleHeader 
        {...defaultProps} 
        isModal={true} 
      />
    );
    
    expect(screen.getByTestId('minimize-icon')).toBeInTheDocument();
  });

  it('should not render enlarge icon when IsenLargeIconVisible is false', () => {
    render(
      <VesselModuleHeader 
        {...defaultProps} 
        IsenLargeIconVisible={false} 
        isModal={false} 
      />
    );
    
    expect(screen.queryByTestId('enlarge-icon')).not.toBeInTheDocument();
  });

  it('should call onViewModeChange when list icon is clicked', () => {
    const onViewModeChange = jest.fn();
    render(
      <VesselModuleHeader 
        {...defaultProps} 
        viewMode="grid"
        onViewModeChange={onViewModeChange}
        IsiconRenderVisible={true}
      />
    );
    
    fireEvent.click(screen.getByTestId('list-icon'));
    expect(onViewModeChange).toHaveBeenCalledWith('list');
  });

  it('should call onViewModeChange when grid icon is clicked', () => {
    const onViewModeChange = jest.fn();
    render(
      <VesselModuleHeader 
        {...defaultProps} 
        viewMode="list"
        onViewModeChange={onViewModeChange}
        IsiconRenderVisible={true}
      />
    );
    
    fireEvent.click(screen.getByTestId('layout-grid-icon'));
    expect(onViewModeChange).toHaveBeenCalledWith('grid');
  });

  it('should call onToggleModal when enlarge icon is clicked', () => {
    const onToggleModal = jest.fn();
    render(
      <VesselModuleHeader 
        {...defaultProps} 
        onToggleModal={onToggleModal}
        IsenLargeIconVisible={true}
        isModal={false}
      />
    );
    
    fireEvent.click(screen.getByTestId('enlarge-icon'));
    expect(onToggleModal).toHaveBeenCalledTimes(1);
  });

  it('should call onToggleModal when minimize icon is clicked', () => {
    const onToggleModal = jest.fn();
    render(
      <VesselModuleHeader 
        {...defaultProps} 
        onToggleModal={onToggleModal}
        isModal={true}
      />
    );
    
    fireEvent.click(screen.getByTestId('minimize-icon'));
    expect(onToggleModal).toHaveBeenCalledTimes(1);
  });

  it('should apply active class to current view mode icon', () => {
    render(
      <VesselModuleHeader
        {...defaultProps}
        viewMode="list"
        IsiconRenderVisible={true}
      />
    );

    const listButton = screen.getByLabelText('List view');
    const gridButton = screen.getByLabelText('Grid view');

    // The active class should be applied based on the current viewMode
    expect(listButton).toHaveClass('active');
    expect(gridButton).not.toHaveClass('active');
  });

  it('should apply active class to grid icon when viewMode is grid', () => {
    render(
      <VesselModuleHeader
        {...defaultProps}
        viewMode="grid"
        IsiconRenderVisible={true}
      />
    );

    const listButton = screen.getByLabelText('List view');
    const gridButton = screen.getByLabelText('Grid view');

    expect(listButton).not.toHaveClass('active');
    expect(gridButton).toHaveClass('active');
  });

  it('should handle undefined optional props', () => {
    const minimalProps = {
      title: 'Minimal Test',
      viewMode: 'list' as const,
      isModal: false,
      onViewModeChange: jest.fn(),
      onToggleModal: jest.fn(),
    };
    
    render(<VesselModuleHeader {...minimalProps} />);
    
    expect(screen.getByText('Minimal Test')).toBeInTheDocument();
    // Icons should not be visible when props are undefined
    expect(screen.queryByTestId('list-icon')).not.toBeInTheDocument();
    expect(screen.queryByTestId('layout-grid-icon')).not.toBeInTheDocument();
    expect(screen.queryByTestId('enlarge-icon')).not.toBeInTheDocument();
  });

  it('should render correct structure', () => {
    const { container } = render(<VesselModuleHeader {...defaultProps} />);

    // Should have the main header container
    const header = container.querySelector('.ra-vessel-module-header');
    expect(header).toBeInTheDocument();

    // Should have title
    const title = container.querySelector('.ra-vessel-module-title');
    expect(title).toBeInTheDocument();

    // Should have controls section
    const controlsSection = container.querySelector('.ra-vessel-module-controls');
    expect(controlsSection).toBeInTheDocument();
  });

  it('should handle all combinations of visibility flags', () => {
    // Test each combination individually to avoid conflicts
    const combinations = [
      { IsiconRenderVisible: true, IsenLargeIconVisible: true, isModal: false },
      { IsiconRenderVisible: true, IsenLargeIconVisible: false, isModal: false },
      { IsiconRenderVisible: false, IsenLargeIconVisible: true, isModal: false },
      { IsiconRenderVisible: false, IsenLargeIconVisible: false, isModal: false },
      { IsiconRenderVisible: true, IsenLargeIconVisible: true, isModal: true },
      { IsiconRenderVisible: false, IsenLargeIconVisible: false, isModal: true },
    ];

    combinations.forEach((combo, index) => {
      const { unmount } = render(
        <VesselModuleHeader
          {...defaultProps}
          {...combo}
          title={`Test ${index}`}
        />
      );

      expect(screen.getByText(`Test ${index}`)).toBeInTheDocument();

      if (combo.IsiconRenderVisible) {
        expect(screen.getByTestId('list-icon')).toBeInTheDocument();
        expect(screen.getByTestId('layout-grid-icon')).toBeInTheDocument();
      } else {
        expect(screen.queryByTestId('list-icon')).not.toBeInTheDocument();
        expect(screen.queryByTestId('layout-grid-icon')).not.toBeInTheDocument();
      }

      if (combo.isModal && combo.IsenLargeIconVisible) {
        expect(screen.getByTestId('minimize-icon')).toBeInTheDocument();
        expect(screen.queryByTestId('enlarge-icon')).not.toBeInTheDocument();
      } else if (combo.IsenLargeIconVisible && !combo.isModal) {
        expect(screen.getByTestId('enlarge-icon')).toBeInTheDocument();
        expect(screen.queryByTestId('minimize-icon')).not.toBeInTheDocument();
      } else {
        expect(screen.queryByTestId('enlarge-icon')).not.toBeInTheDocument();
        expect(screen.queryByTestId('minimize-icon')).not.toBeInTheDocument();
      }

      // Clean up after each test
      unmount();
    });
  });
});
