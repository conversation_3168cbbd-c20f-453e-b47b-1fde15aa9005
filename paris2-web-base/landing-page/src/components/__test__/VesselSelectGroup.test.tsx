import React from 'react';
import { render, screen } from '@testing-library/react';
import { VesselSelectGroup } from '../dashboard/VesselWidget/VesselSelectGroup';
import { VesselGroup } from '../../types/types';

// Mock the VesselDropdown component
jest.mock('../dashboard/VesselWidget/VesselDropdown', () => ({
  VesselDropdown: ({
    groups,
    selectedVessels,
    onSelectionChange,
    placeholder,
    width,
    isSearchBoxVisible,
    isSelectAllVisible
  }: any) => (
    <div data-testid="vessel-dropdown">
      <div>Placeholder: {placeholder}</div>
      <div>Width: {width}</div>
      <div>Groups: {groups?.length || 0}</div>
      <div>Selected: {selectedVessels?.join(', ') || 'none'}</div>
      <div>Search Visible: {isSearchBoxVisible?.toString()}</div>
      <div>Select All Visible: {isSelectAllVisible?.toString()}</div>
      <button onClick={() => onSelectionChange(['test vessel'])}>
        Trigger Selection Change
      </button>
    </div>
  ),
}));

describe('VesselSelectGroup', () => {
  const mockVesselGroups: VesselGroup[] = [
    {
      id: 1,
      title: 'Group 1',
      vessels: [
        { vessel_id: 1, name: 'Vessel A', vessel_ownership_id: 101 },
        { vessel_id: 2, name: 'Vessel B', vessel_ownership_id: 102 },
      ],
    },
    {
      id: 2,
      title: 'Group 2',
      vessels: [
        { vessel_id: 3, name: 'Vessel C', vessel_ownership_id: 103 },
      ],
    },
  ];

  const defaultProps = {
    index: 0,
    config: {
      placeholder: 'Select vessels',
      width: '300px',
    },
    selectedVessels: ['Vessel A'],
    groups: mockVesselGroups,
    onChange: jest.fn(),
    isSearchBoxVisible: true,
    isSelectAllVisible: true,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render VesselDropdown with correct props', () => {
    render(<VesselSelectGroup {...defaultProps} />);
    
    expect(screen.getByTestId('vessel-dropdown')).toBeInTheDocument();
    expect(screen.getByText('Placeholder: Select vessels')).toBeInTheDocument();
    expect(screen.getByText('Width: 300px')).toBeInTheDocument();
    expect(screen.getByText('Groups: 2')).toBeInTheDocument();
    expect(screen.getByText('Selected: Vessel A')).toBeInTheDocument();
    expect(screen.getByText('Search Visible: true')).toBeInTheDocument();
    expect(screen.getByText('Select All Visible: true')).toBeInTheDocument();
  });

  it('should handle missing groups prop', () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    render(
      <VesselSelectGroup 
        {...defaultProps} 
        groups={undefined as any}
      />
    );
    
    expect(consoleErrorSpy).toHaveBeenCalledWith('VesselSelectGroup: `groups` prop is missing.');
    expect(screen.queryByTestId('vessel-dropdown')).not.toBeInTheDocument();
    
    consoleErrorSpy.mockRestore();
  });

  it('should handle null groups prop', () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    render(
      <VesselSelectGroup 
        {...defaultProps} 
        groups={null as any}
      />
    );
    
    expect(consoleErrorSpy).toHaveBeenCalledWith('VesselSelectGroup: `groups` prop is missing.');
    expect(screen.queryByTestId('vessel-dropdown')).not.toBeInTheDocument();
    
    consoleErrorSpy.mockRestore();
  });

  it('should call onChange with correct parameters when selection changes', () => {
    const onChange = jest.fn();
    
    render(
      <VesselSelectGroup 
        {...defaultProps} 
        index={2}
        onChange={onChange}
      />
    );
    
    const triggerButton = screen.getByText('Trigger Selection Change');
    triggerButton.click();
    
    expect(onChange).toHaveBeenCalledWith(2, ['test vessel']);
  });

  it('should handle empty selectedVessels array', () => {
    render(
      <VesselSelectGroup 
        {...defaultProps} 
        selectedVessels={[]}
      />
    );
    
    expect(screen.getByText('Selected: none')).toBeInTheDocument();
  });

  it('should handle multiple selected vessels', () => {
    render(
      <VesselSelectGroup 
        {...defaultProps} 
        selectedVessels={['Vessel A', 'Vessel B', 'Vessel C']}
      />
    );
    
    expect(screen.getByText('Selected: Vessel A, Vessel B, Vessel C')).toBeInTheDocument();
  });

  it('should handle config with undefined placeholder', () => {
    render(
      <VesselSelectGroup 
        {...defaultProps} 
        config={{
          placeholder: undefined,
          width: '250px',
        }}
      />
    );
    
    expect(screen.getByText('Placeholder:')).toBeInTheDocument(); // undefined becomes empty
    expect(screen.getByText('Width: 250px')).toBeInTheDocument();
  });

  it('should handle config with undefined width', () => {
    render(
      <VesselSelectGroup 
        {...defaultProps} 
        config={{
          placeholder: 'Test placeholder',
          width: undefined,
        }}
      />
    );
    
    expect(screen.getByText('Placeholder: Test placeholder')).toBeInTheDocument();
    expect(screen.getByText('Width: 200px')).toBeInTheDocument(); // Default width should be applied
  });

  it('should handle isSearchBoxVisible false', () => {
    render(
      <VesselSelectGroup 
        {...defaultProps} 
        isSearchBoxVisible={false}
      />
    );
    
    expect(screen.getByText('Search Visible: false')).toBeInTheDocument();
  });

  it('should handle isSelectAllVisible false', () => {
    render(
      <VesselSelectGroup 
        {...defaultProps} 
        isSelectAllVisible={false}
      />
    );
    
    expect(screen.getByText('Select All Visible: false')).toBeInTheDocument();
  });

  it('should handle undefined optional props', () => {
    render(
      <VesselSelectGroup 
        index={0}
        config={{
          placeholder: 'Test',
          width: '300px',
        }}
        selectedVessels={[]}
        groups={mockVesselGroups}
        onChange={jest.fn()}
      />
    );
    
    expect(screen.getByTestId('vessel-dropdown')).toBeInTheDocument();
    expect(screen.getByText('Search Visible:')).toBeInTheDocument(); // undefined becomes empty
    expect(screen.getByText('Select All Visible:')).toBeInTheDocument(); // undefined becomes empty
  });

  it('should be memoized and not re-render with same props', () => {
    const { rerender } = render(<VesselSelectGroup {...defaultProps} />);
    
    const firstRender = screen.getByTestId('vessel-dropdown');
    
    // Re-render with same props
    rerender(<VesselSelectGroup {...defaultProps} />);
    
    const secondRender = screen.getByTestId('vessel-dropdown');
    
    // Component should be the same instance due to React.memo
    expect(firstRender).toBe(secondRender);
  });

  it('should re-render when props change', () => {
    const { rerender } = render(<VesselSelectGroup {...defaultProps} />);
    
    expect(screen.getByText('Selected: Vessel A')).toBeInTheDocument();
    
    // Re-render with different selectedVessels
    rerender(
      <VesselSelectGroup 
        {...defaultProps} 
        selectedVessels={['Vessel B']}
      />
    );
    
    expect(screen.getByText('Selected: Vessel B')).toBeInTheDocument();
  });

  it('should handle different index values', () => {
    const onChange = jest.fn();
    
    render(
      <VesselSelectGroup 
        {...defaultProps} 
        index={5}
        onChange={onChange}
      />
    );
    
    const triggerButton = screen.getByText('Trigger Selection Change');
    triggerButton.click();
    
    expect(onChange).toHaveBeenCalledWith(5, ['test vessel']);
  });

  it('should handle empty groups array', () => {
    render(
      <VesselSelectGroup 
        {...defaultProps} 
        groups={[]}
      />
    );
    
    expect(screen.getByText('Groups: 0')).toBeInTheDocument();
  });

  it('should handle groups with empty vessels', () => {
    const emptyVesselGroups: VesselGroup[] = [
      {
        id: 1,
        title: 'Empty Group',
        vessels: [],
      },
    ];
    
    render(
      <VesselSelectGroup 
        {...defaultProps} 
        groups={emptyVesselGroups}
      />
    );
    
    expect(screen.getByText('Groups: 1')).toBeInTheDocument();
  });

  it('should render with wrapper div having correct class', () => {
    const { container } = render(<VesselSelectGroup {...defaultProps} />);

    // Since it's using CSS modules, we'll just check that the wrapper div exists
    const wrapper = container.firstChild;
    expect(wrapper).toBeInTheDocument();
    expect(wrapper).toBeInstanceOf(HTMLDivElement);
  });

  it('should handle callback memoization correctly', () => {
    const onChange = jest.fn();
    
    const { rerender } = render(
      <VesselSelectGroup 
        {...defaultProps} 
        index={1}
        onChange={onChange}
      />
    );
    
    const triggerButton = screen.getByText('Trigger Selection Change');
    triggerButton.click();
    
    expect(onChange).toHaveBeenCalledWith(1, ['test vessel']);
    
    // Re-render with same props - callback should be memoized
    rerender(
      <VesselSelectGroup 
        {...defaultProps} 
        index={1}
        onChange={onChange}
      />
    );
    
    // Clear previous calls
    onChange.mockClear();
    
    triggerButton.click();
    expect(onChange).toHaveBeenCalledWith(1, ['test vessel']);
  });
});
