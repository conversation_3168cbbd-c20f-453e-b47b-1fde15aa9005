import React from 'react';
import { render, screen } from '@testing-library/react';
import { VesselSelectors } from '../dashboard/VesselWidget/VesselSelectors';
import { MultiVesselSelectConfig } from '../../types/types';

// Mock the VesselSelectGroup component
jest.mock('../dashboard/VesselWidget/VesselSelectGroup', () => ({
  VesselSelectGroup: ({
    index,
    config,
    selectedVessels,
    groups,
    onChange,
    isSearchBoxVisible,
    isSelectAllVisible
  }: any) => (
    <div data-testid={`vessel-select-group-${index}`}>
      <div>Index: {index}</div>
      <div>Placeholder: {config?.placeholder || 'none'}</div>
      <div>Width: {config?.width || 'none'}</div>
      <div>Groups: {groups?.length || 0}</div>
      <div>Selected: {selectedVessels?.join(', ') || 'none'}</div>
      <div>Search Visible: {isSearchBoxVisible?.toString()}</div>
      <div>Select All Visible: {isSelectAllVisible?.toString()}</div>
      <button onClick={() => onChange(index, ['test'])}>
        Trigger Change {index}
      </button>
    </div>
  ),
}));

describe('VesselSelectors', () => {
  const mockMultiVesselSelects: MultiVesselSelectConfig[] = [
    {
      placeholder: 'All Vessels',
      width: '300px',
      groups: [
        {
          id: 1,
          title: 'Group 1',
          vessels: [
            { vessel_id: 1, name: 'Vessel A', vessel_ownership_id: 101 },
            { vessel_id: 2, name: 'Vessel B', vessel_ownership_id: 102 },
          ],
        },
      ],
      isSearchBoxVisible: true,
      isSelectAllVisible: true,
    },
    {
      placeholder: 'Level of R.A.',
      width: '250px',
      groups: [
        {
          id: 2,
          title: 'Group 2',
          vessels: [
            { vessel_id: 3, name: 'Vessel C', vessel_ownership_id: 103 },
          ],
        },
      ],
      isSearchBoxVisible: false,
      isSelectAllVisible: false,
    },
  ];

  const defaultProps = {
    multiVesselSelects: mockMultiVesselSelects,
    selectStates: [['Vessel A'], ['Vessel C']],
    onSelectChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render all VesselSelectGroup components', () => {
    render(<VesselSelectors {...defaultProps} />);
    
    expect(screen.getByTestId('vessel-select-group-0')).toBeInTheDocument();
    expect(screen.getByTestId('vessel-select-group-1')).toBeInTheDocument();
  });

  it('should pass correct props to each VesselSelectGroup', () => {
    render(<VesselSelectors {...defaultProps} />);

    // Check first group
    const firstGroup = screen.getByTestId('vessel-select-group-0');
    expect(firstGroup).toHaveTextContent('Index: 0');
    expect(firstGroup).toHaveTextContent('Placeholder: All Vessels');
    expect(firstGroup).toHaveTextContent('Width: 300px');
    expect(firstGroup).toHaveTextContent('Groups: 1');
    expect(firstGroup).toHaveTextContent('Selected: Vessel A');

    // Check second group
    const secondGroup = screen.getByTestId('vessel-select-group-1');
    expect(secondGroup).toHaveTextContent('Index: 1');
    expect(secondGroup).toHaveTextContent('Placeholder: Level of R.A.');
    expect(secondGroup).toHaveTextContent('Width: 250px');
    expect(secondGroup).toHaveTextContent('Groups: 1');
    expect(secondGroup).toHaveTextContent('Selected: Vessel C');
  });

  it('should pass correct visibility flags to each group', () => {
    render(<VesselSelectors {...defaultProps} />);
    
    // First group should have search and select all visible
    const firstGroupElements = screen.getByTestId('vessel-select-group-0');
    expect(firstGroupElements).toHaveTextContent('Search Visible: true');
    expect(firstGroupElements).toHaveTextContent('Select All Visible: true');
    
    // Second group should have search and select all hidden
    const secondGroupElements = screen.getByTestId('vessel-select-group-1');
    expect(secondGroupElements).toHaveTextContent('Search Visible: false');
    expect(secondGroupElements).toHaveTextContent('Select All Visible: false');
  });

  it('should return null when multiVesselSelects is not an array', () => {
    render(
      <VesselSelectors 
        multiVesselSelects={null as any}
        selectStates={[]}
        onSelectChange={jest.fn()}
      />
    );
    
    expect(screen.queryByTestId('vessel-select-group-0')).not.toBeInTheDocument();
  });

  it('should return null when multiVesselSelects is undefined', () => {
    render(
      <VesselSelectors 
        multiVesselSelects={undefined as any}
        selectStates={[]}
        onSelectChange={jest.fn()}
      />
    );
    
    expect(screen.queryByTestId('vessel-select-group-0')).not.toBeInTheDocument();
  });

  it('should handle empty multiVesselSelects array', () => {
    render(
      <VesselSelectors 
        multiVesselSelects={[]}
        selectStates={[]}
        onSelectChange={jest.fn()}
      />
    );
    
    expect(screen.queryByTestId('vessel-select-group-0')).not.toBeInTheDocument();
  });

  it('should skip items without groups', () => {
    const selectsWithMissingGroups: MultiVesselSelectConfig[] = [
      {
        placeholder: 'Valid Group',
        width: '300px',
        groups: [
          {
            id: 1,
            title: 'Group 1',
            vessels: [
              { vessel_id: 1, name: 'Vessel A', vessel_ownership_id: 101 },
            ],
          },
        ],
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
      {
        placeholder: 'Invalid Group',
        width: '250px',
        groups: null as any,
        isSearchBoxVisible: false,
        isSelectAllVisible: false,
      },
      {
        placeholder: 'Another Valid Group',
        width: '200px',
        groups: [
          {
            id: 2,
            title: 'Group 2',
            vessels: [
              { vessel_id: 2, name: 'Vessel B', vessel_ownership_id: 102 },
            ],
          },
        ],
        isSearchBoxVisible: true,
        isSelectAllVisible: false,
      },
    ];
    
    render(
      <VesselSelectors 
        multiVesselSelects={selectsWithMissingGroups}
        selectStates={[['Vessel A'], [], ['Vessel B']]}
        onSelectChange={jest.fn()}
      />
    );
    
    // Should render first and third groups, skip second
    expect(screen.getByTestId('vessel-select-group-0')).toBeInTheDocument();
    expect(screen.queryByTestId('vessel-select-group-1')).not.toBeInTheDocument();
    expect(screen.getByTestId('vessel-select-group-2')).toBeInTheDocument();
    
    expect(screen.getByText('Placeholder: Valid Group')).toBeInTheDocument();
    expect(screen.getByText('Placeholder: Another Valid Group')).toBeInTheDocument();
    expect(screen.queryByText('Placeholder: Invalid Group')).not.toBeInTheDocument();
  });

  it('should handle missing selectStates for some indices', () => {
    render(
      <VesselSelectors 
        multiVesselSelects={mockMultiVesselSelects}
        selectStates={[['Vessel A']]} // Missing second state
        onSelectChange={jest.fn()}
      />
    );
    
    expect(screen.getByText('Selected: Vessel A')).toBeInTheDocument();
    expect(screen.getByText('Selected: none')).toBeInTheDocument(); // Should default to empty
  });

  it('should call onSelectChange with correct parameters', () => {
    const onSelectChange = jest.fn();
    
    render(
      <VesselSelectors 
        {...defaultProps}
        onSelectChange={onSelectChange}
      />
    );
    
    // Trigger change on first group
    const firstGroupButton = screen.getByText('Trigger Change 0');
    firstGroupButton.click();
    
    expect(onSelectChange).toHaveBeenCalledWith(0, ['test']);
    
    // Trigger change on second group
    const secondGroupButton = screen.getByText('Trigger Change 1');
    secondGroupButton.click();
    
    expect(onSelectChange).toHaveBeenCalledWith(1, ['test']);
  });

  it('should use placeholder as key when available', () => {
    const { container } = render(<VesselSelectors {...defaultProps} />);
    
    // Check that components are rendered (keys are internal to React)
    expect(screen.getByTestId('vessel-select-group-0')).toBeInTheDocument();
    expect(screen.getByTestId('vessel-select-group-1')).toBeInTheDocument();
  });

  it('should fallback to index as key when placeholder is missing', () => {
    const selectsWithoutPlaceholder: MultiVesselSelectConfig[] = [
      {
        placeholder: '',
        width: '300px',
        groups: [
          {
            id: 1,
            title: 'Group 1',
            vessels: [
              { vessel_id: 1, name: 'Vessel A', vessel_ownership_id: 101 },
            ],
          },
        ],
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
    ];
    
    render(
      <VesselSelectors 
        multiVesselSelects={selectsWithoutPlaceholder}
        selectStates={[['Vessel A']]}
        onSelectChange={jest.fn()}
      />
    );
    
    expect(screen.getByTestId('vessel-select-group-0')).toBeInTheDocument();
  });

  it('should render container with correct class', () => {
    const { container } = render(<VesselSelectors {...defaultProps} />);
    
    const containerDiv = container.querySelector('.ra-vessel-selects-container');
    expect(containerDiv).toBeInTheDocument();
  });

  it('should handle undefined optional properties in config', () => {
    const selectsWithUndefinedProps: MultiVesselSelectConfig[] = [
      {
        placeholder: 'Test',
        width: '300px',
        groups: [
          {
            id: 1,
            title: 'Group 1',
            vessels: [
              { vessel_id: 1, name: 'Vessel A', vessel_ownership_id: 101 },
            ],
          },
        ],
        // isSearchBoxVisible and isSelectAllVisible are undefined
      },
    ];
    
    render(
      <VesselSelectors 
        multiVesselSelects={selectsWithUndefinedProps}
        selectStates={[['Vessel A']]}
        onSelectChange={jest.fn()}
      />
    );
    
    expect(screen.getByTestId('vessel-select-group-0')).toBeInTheDocument();
    expect(screen.getByText('Search Visible:')).toBeInTheDocument(); // undefined becomes empty string
    expect(screen.getByText('Select All Visible:')).toBeInTheDocument(); // undefined becomes empty string
  });

  it('should handle large number of selects', () => {
    const manySelects: MultiVesselSelectConfig[] = Array.from({ length: 10 }, (_, i) => ({
      placeholder: `Select ${i}`,
      width: '200px',
      groups: [
        {
          id: i,
          title: `Group ${i}`,
          vessels: [
            { vessel_id: i, name: `Vessel ${i}`, vessel_ownership_id: 100 + i },
          ],
        },
      ],
      isSearchBoxVisible: i % 2 === 0,
      isSelectAllVisible: i % 3 === 0,
    }));
    
    const manyStates = Array.from({ length: 10 }, (_, i) => [`Vessel ${i}`]);
    
    render(
      <VesselSelectors 
        multiVesselSelects={manySelects}
        selectStates={manyStates}
        onSelectChange={jest.fn()}
      />
    );
    
    // Check that all 10 groups are rendered
    for (let i = 0; i < 10; i++) {
      expect(screen.getByTestId(`vessel-select-group-${i}`)).toBeInTheDocument();
      expect(screen.getByText(`Placeholder: Select ${i}`)).toBeInTheDocument();
    }
  });
});
