import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { VesselDropdown } from '../dashboard/VesselWidget/VesselDropdown';
import { VesselGroup } from '../../types/types';

// Mock the hooks and components
jest.mock('../../hooks/useDropdown', () => ({
  useDropdown: jest.fn(),
}));

jest.mock('lucide-react', () => ({
  ChevronDown: ({ size, className }: any) => (
    <div data-testid="chevron-down" className={className}>
      ChevronDown
    </div>
  ),
}));

jest.mock('../dashboard/VesselWidget/VesselDropdownMenu', () => ({
  VesselDropdownMenu: ({ searchTerm, onSearchChange, filteredGroups, selectedVessels, onToggleVessel, onToggleGroup, isAllSelected, onToggleAll, isSearchBoxVisible, isSelectAllVisible }: any) => (
    <div data-testid="vessel-dropdown-menu">
      <div>Search: {searchTerm}</div>
      <div>Groups: {filteredGroups.length}</div>
      <div>Selected: {selectedVessels.join(', ')}</div>
      <div>All Selected: {isAllSelected.toString()}</div>
      <div>Search Visible: {isSearchBoxVisible.toString()}</div>
      <div>Select All Visible: {isSelectAllVisible.toString()}</div>
      <button onClick={() => onSearchChange('test')}>Change Search</button>
      <button onClick={() => onToggleVessel('Test Vessel')}>Toggle Vessel</button>
      <button onClick={() => onToggleGroup({ id: 1, title: 'Test', vessels: [] })}>Toggle Group</button>
      <button onClick={onToggleAll}>Toggle All</button>
    </div>
  ),
}));

describe('VesselDropdown', () => {
  const mockVesselGroups: VesselGroup[] = [
    {
      id: 1,
      title: 'Group 1',
      vessels: [
        { vessel_id: 1, name: 'Vessel Alpha', vessel_ownership_id: 101 },
        { vessel_id: 2, name: 'Vessel Beta', vessel_ownership_id: 102 },
      ],
    },
    {
      id: 2,
      title: 'Group 2',
      vessels: [
        { vessel_id: 3, name: 'Vessel Gamma', vessel_ownership_id: 103 },
        { vessel_id: 4, name: 'Vessel Delta', vessel_ownership_id: 104 },
      ],
    },
  ];

  const defaultProps = {
    groups: mockVesselGroups,
    selectedVessels: [],
    onSelectionChange: jest.fn(),
    placeholder: 'Select vessels',
    width: '300px',
    isSearchBoxVisible: true,
    isSelectAllVisible: true,
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Set default mock implementation for useDropdown
    const useDropdown = require('../../hooks/useDropdown').useDropdown;
    useDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: false,
      toggleDropdown: jest.fn(),
    });
  });

  it('should render with default props', () => {
    render(<VesselDropdown {...defaultProps} />);
    
    expect(screen.getByText('Select vessels')).toBeInTheDocument();
    expect(screen.getByTestId('chevron-down')).toBeInTheDocument();
  });

  it('should display placeholder when no vessels are selected', () => {
    render(<VesselDropdown {...defaultProps} selectedVessels={[]} />);
    
    expect(screen.getByText('Select vessels')).toBeInTheDocument();
  });

  it('should display selected vessel names when vessels are selected', () => {
    render(
      <VesselDropdown 
        {...defaultProps} 
        selectedVessels={['Vessel Alpha']}
      />
    );
    
    expect(screen.getByText('Vessel Alpha')).toBeInTheDocument();
  });

  it('should display multiple selected vessels correctly', () => {
    render(
      <VesselDropdown 
        {...defaultProps} 
        selectedVessels={['Vessel Alpha', 'Vessel Beta']}
      />
    );
    
    expect(screen.getByText('Vessel Alpha, Vessel Beta')).toBeInTheDocument();
  });

  it('should display truncated text with "more" badge when more than 2 vessels are selected', () => {
    render(
      <VesselDropdown 
        {...defaultProps} 
        selectedVessels={['Vessel Alpha', 'Vessel Beta', 'Vessel Gamma']}
      />
    );
    
    expect(screen.getByText('Vessel Alpha, Vessel Beta')).toBeInTheDocument();
    expect(screen.getByText('+1 more')).toBeInTheDocument();
  });

  it('should show correct count in more badge for many selected vessels', () => {
    render(
      <VesselDropdown 
        {...defaultProps} 
        selectedVessels={['Vessel Alpha', 'Vessel Beta', 'Vessel Gamma', 'Vessel Delta', 'Vessel Echo']}
      />
    );
    
    expect(screen.getByText('Vessel Alpha, Vessel Beta')).toBeInTheDocument();
    expect(screen.getByText('+3 more')).toBeInTheDocument();
  });

  it('should apply correct width class', () => {
    const { container } = render(<VesselDropdown {...defaultProps} width="400px" />);

    // Find the main dropdown container (first div)
    const dropdownContainer = container.querySelector('div');
    expect(dropdownContainer).toBeInTheDocument();
  });

  it('should handle undefined width', () => {
    const { container } = render(<VesselDropdown {...defaultProps} width={undefined} />);

    // Find the main dropdown container (first div)
    const dropdownContainer = container.querySelector('div');
    expect(dropdownContainer).toBeInTheDocument();
  });

  it('should filter groups based on search term', () => {
    // Mock useDropdown to return isOpen: true
    const useDropdown = require('../../hooks/useDropdown').useDropdown;
    useDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: true,
      toggleDropdown: jest.fn(),
    });

    render(<VesselDropdown {...defaultProps} />);

    // The dropdown menu should be rendered when open
    expect(screen.getByTestId('vessel-dropdown-menu')).toBeInTheDocument();
  });

  it('should not render dropdown menu when closed', () => {
    // Mock useDropdown to return isOpen: false
    const useDropdown = require('../../hooks/useDropdown').useDropdown;
    useDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: false,
      toggleDropdown: jest.fn(),
    });

    render(<VesselDropdown {...defaultProps} />);

    expect(screen.queryByTestId('vessel-dropdown-menu')).not.toBeInTheDocument();
  });

  it('should call onSelectionChange when vessel is toggled', () => {
    const onSelectionChange = jest.fn();
    const useDropdown = require('../../hooks/useDropdown').useDropdown;
    useDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: true,
      toggleDropdown: jest.fn(),
    });

    render(
      <VesselDropdown
        {...defaultProps}
        onSelectionChange={onSelectionChange}
      />
    );

    fireEvent.click(screen.getByText('Toggle Vessel'));
    expect(onSelectionChange).toHaveBeenCalled();
  });

  it('should handle vessel selection correctly', () => {
    const onSelectionChange = jest.fn();
    const useDropdown = require('../../hooks/useDropdown').useDropdown;
    useDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: true,
      toggleDropdown: jest.fn(),
    });

    render(
      <VesselDropdown
        {...defaultProps}
        selectedVessels={['Vessel Alpha']}
        onSelectionChange={onSelectionChange}
      />
    );

    // Simulate toggling a vessel that's already selected (should remove it)
    fireEvent.click(screen.getByText('Toggle Vessel'));
    expect(onSelectionChange).toHaveBeenCalled();
  });

  it('should handle group selection correctly', () => {
    const onSelectionChange = jest.fn();
    const useDropdown = require('../../hooks/useDropdown').useDropdown;
    useDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: true,
      toggleDropdown: jest.fn(),
    });

    render(
      <VesselDropdown
        {...defaultProps}
        onSelectionChange={onSelectionChange}
      />
    );

    fireEvent.click(screen.getByText('Toggle Group'));
    expect(onSelectionChange).toHaveBeenCalled();
  });

  it('should handle select all functionality', () => {
    const onSelectionChange = jest.fn();
    const useDropdown = require('../../hooks/useDropdown').useDropdown;
    useDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: true,
      toggleDropdown: jest.fn(),
    });

    render(
      <VesselDropdown
        {...defaultProps}
        onSelectionChange={onSelectionChange}
      />
    );

    fireEvent.click(screen.getByText('Toggle All'));
    expect(onSelectionChange).toHaveBeenCalled();
  });

  it('should pass correct props to VesselDropdownMenu', () => {
    const useDropdown = require('../../hooks/useDropdown').useDropdown;
    useDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: true,
      toggleDropdown: jest.fn(),
    });

    render(
      <VesselDropdown
        {...defaultProps}
        selectedVessels={['Vessel Alpha']}
        isSearchBoxVisible={false}
        isSelectAllVisible={false}
      />
    );

    expect(screen.getByText('Selected: Vessel Alpha')).toBeInTheDocument();
    expect(screen.getByText('Search Visible: false')).toBeInTheDocument();
    expect(screen.getByText('Select All Visible: false')).toBeInTheDocument();
  });

  it('should handle empty groups array', () => {
    render(<VesselDropdown {...defaultProps} groups={[]} />);
    
    expect(screen.getByText('Select vessels')).toBeInTheDocument();
  });

  it('should handle groups with empty vessels arrays', () => {
    const emptyGroups: VesselGroup[] = [
      {
        id: 1,
        title: 'Empty Group',
        vessels: [],
      },
    ];

    render(<VesselDropdown {...defaultProps} groups={emptyGroups} />);
    
    expect(screen.getByText('Select vessels')).toBeInTheDocument();
  });

  it('should have correct accessibility attributes', () => {
    render(<VesselDropdown {...defaultProps} />);

    // Find the dropdown header by its aria attributes
    const dropdownHeader = screen.getByRole('button', { expanded: false });
    expect(dropdownHeader).toHaveAttribute('aria-haspopup', 'listbox');
    expect(dropdownHeader).toHaveAttribute('aria-expanded', 'false');
  });

  it('should update aria-expanded when dropdown is open', () => {
    const useDropdown = require('../../hooks/useDropdown').useDropdown;
    useDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: true,
      toggleDropdown: jest.fn(),
    });

    render(<VesselDropdown {...defaultProps} />);

    // Find the dropdown header by its aria attributes
    const dropdownHeader = screen.getByRole('button', { expanded: true });
    expect(dropdownHeader).toHaveAttribute('aria-expanded', 'true');
  });

  it('should call toggleDropdown when header is clicked', () => {
    const toggleDropdown = jest.fn();
    const useDropdown = require('../../hooks/useDropdown').useDropdown;
    useDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: false,
      toggleDropdown,
    });

    render(<VesselDropdown {...defaultProps} />);

    const dropdownHeader = screen.getByRole('button', { expanded: false });
    fireEvent.click(dropdownHeader);

    expect(toggleDropdown).toHaveBeenCalledTimes(1);
  });
});
