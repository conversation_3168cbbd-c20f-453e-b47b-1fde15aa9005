import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { VesselDropdownMenu } from '../dashboard/VesselWidget/VesselDropdownMenu';
import { VesselGroup } from '../../../types/types';

// Mock lucide-react Search icon
jest.mock('lucide-react', () => ({
  Search: ({ size, className }: any) => (
    <div data-testid="search-icon" className={className}>
      Search
    </div>
  ),
}));

describe('VesselDropdownMenu', () => {
  const mockVesselGroups: VesselGroup[] = [
    {
      id: 1,
      title: 'Group 1',
      vessels: [
        { vessel_id: 1, name: 'Vessel A', vessel_ownership_id: 101 },
        { vessel_id: 2, name: 'Vessel B', vessel_ownership_id: 102 },
      ],
    },
    {
      id: 2,
      title: 'Group 2',
      vessels: [
        { vessel_id: 3, name: 'Vessel C', vessel_ownership_id: 103 },
        { vessel_id: 4, name: 'Vessel D', vessel_ownership_id: 104 },
      ],
    },
  ];

  const defaultProps = {
    searchTerm: '',
    onSearchChange: jest.fn(),
    filteredGroups: mockVesselGroups,
    selectedVessels: ['Vessel A'],
    onToggleVessel: jest.fn(),
    onToggleGroup: jest.fn(),
    isAllSelected: false,
    onToggleAll: jest.fn(),
    isSearchBoxVisible: true,
    isSelectAllVisible: true,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('SearchInput component', () => {
    it('should render search input when isSearchBoxVisible is true', () => {
      render(<VesselDropdownMenu {...defaultProps} />);
      
      expect(screen.getByTestId('search-icon')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Search')).toBeInTheDocument();
    });

    it('should not render search input when isSearchBoxVisible is false', () => {
      render(<VesselDropdownMenu {...defaultProps} isSearchBoxVisible={false} />);
      
      expect(screen.queryByTestId('search-icon')).not.toBeInTheDocument();
      expect(screen.queryByPlaceholderText('Search')).not.toBeInTheDocument();
    });

    it('should display current search term', () => {
      render(<VesselDropdownMenu {...defaultProps} searchTerm="test search" />);
      
      const searchInput = screen.getByPlaceholderText('Search') as HTMLInputElement;
      expect(searchInput.value).toBe('test search');
    });

    it('should call onSearchChange when typing in search input', () => {
      const onSearchChange = jest.fn();
      render(<VesselDropdownMenu {...defaultProps} onSearchChange={onSearchChange} />);
      
      const searchInput = screen.getByPlaceholderText('Search');
      fireEvent.change(searchInput, { target: { value: 'new search' } });
      
      expect(onSearchChange).toHaveBeenCalledWith('new search');
    });

    it('should have correct accessibility attributes', () => {
      render(<VesselDropdownMenu {...defaultProps} />);
      
      const searchInput = screen.getByPlaceholderText('Search');
      expect(searchInput).toHaveAttribute('aria-label', 'Search vessels');
      expect(searchInput).toHaveAttribute('type', 'text');
    });
  });

  describe('VesselGroupList component', () => {
    it('should render all vessel groups and vessels', () => {
      render(<VesselDropdownMenu {...defaultProps} />);
      
      expect(screen.getByText('Vessel A')).toBeInTheDocument();
      expect(screen.getByText('Vessel B')).toBeInTheDocument();
      expect(screen.getByText('Vessel C')).toBeInTheDocument();
      expect(screen.getByText('Vessel D')).toBeInTheDocument();
    });

    it('should show selected vessels as checked', () => {
      render(<VesselDropdownMenu {...defaultProps} selectedVessels={['Vessel A', 'Vessel C']} />);
      
      const checkboxes = screen.getAllByRole('checkbox');
      const vesselACheckbox = checkboxes.find(cb => 
        cb.parentElement?.textContent?.includes('Vessel A')
      ) as HTMLInputElement;
      const vesselCCheckbox = checkboxes.find(cb => 
        cb.parentElement?.textContent?.includes('Vessel C')
      ) as HTMLInputElement;
      
      expect(vesselACheckbox?.checked).toBe(true);
      expect(vesselCCheckbox?.checked).toBe(true);
    });

    it('should call onToggleVessel when vessel checkbox is clicked', () => {
      const onToggleVessel = jest.fn();
      render(<VesselDropdownMenu {...defaultProps} onToggleVessel={onToggleVessel} />);
      
      const vesselACheckbox = screen.getAllByRole('checkbox').find(cb => 
        cb.parentElement?.textContent?.includes('Vessel A')
      );
      
      fireEvent.click(vesselACheckbox!);
      expect(onToggleVessel).toHaveBeenCalledWith('Vessel A');
    });

    it('should render empty state when no filtered groups', () => {
      render(<VesselDropdownMenu {...defaultProps} filteredGroups={[]} />);
      
      expect(screen.queryByText('Vessel A')).not.toBeInTheDocument();
      expect(screen.queryByText('Vessel B')).not.toBeInTheDocument();
    });

    it('should handle groups with no vessels', () => {
      const emptyGroups: VesselGroup[] = [
        {
          id: 1,
          title: 'Empty Group',
          vessels: [],
        },
      ];
      
      render(<VesselDropdownMenu {...defaultProps} filteredGroups={emptyGroups} />);
      
      // Should not crash and should not render any vessels
      expect(screen.queryByRole('checkbox')).not.toBeInTheDocument();
    });
  });

  describe('DropdownFooter component', () => {
    it('should render select all footer when isSelectAllVisible is true', () => {
      render(<VesselDropdownMenu {...defaultProps} />);
      
      expect(screen.getByText('Select All')).toBeInTheDocument();
    });

    it('should not render select all footer when isSelectAllVisible is false', () => {
      render(<VesselDropdownMenu {...defaultProps} isSelectAllVisible={false} />);
      
      expect(screen.queryByText('Select All')).not.toBeInTheDocument();
    });

    it('should show "Clear All" when all items are selected', () => {
      render(<VesselDropdownMenu {...defaultProps} isAllSelected={true} />);

      expect(screen.getByText('Clear All')).toBeInTheDocument();
    });

    it('should call onToggleAll when select all is clicked', () => {
      const onToggleAll = jest.fn();
      render(<VesselDropdownMenu {...defaultProps} onToggleAll={onToggleAll} />);

      fireEvent.click(screen.getByText('Select All'));
      expect(onToggleAll).toHaveBeenCalledTimes(1);
    });

    it('should call onToggleAll when clear all is clicked', () => {
      const onToggleAll = jest.fn();
      render(<VesselDropdownMenu {...defaultProps} isAllSelected={true} onToggleAll={onToggleAll} />);

      fireEvent.click(screen.getByText('Clear All'));
      expect(onToggleAll).toHaveBeenCalledTimes(1);
    });
  });

  describe('Integration tests', () => {
    it('should render all components together correctly', () => {
      render(<VesselDropdownMenu {...defaultProps} />);
      
      // Search should be visible
      expect(screen.getByPlaceholderText('Search')).toBeInTheDocument();
      
      // Vessels should be visible
      expect(screen.getByText('Vessel A')).toBeInTheDocument();
      expect(screen.getByText('Vessel B')).toBeInTheDocument();
      
      // Footer should be visible
      expect(screen.getByText('Select All')).toBeInTheDocument();
    });

    it('should handle minimal configuration', () => {
      render(
        <VesselDropdownMenu 
          {...defaultProps} 
          isSearchBoxVisible={false}
          isSelectAllVisible={false}
        />
      );
      
      // Only vessel list should be visible
      expect(screen.queryByPlaceholderText('Search')).not.toBeInTheDocument();
      expect(screen.getByText('Vessel A')).toBeInTheDocument();
      expect(screen.queryByText('Select All')).not.toBeInTheDocument();
    });

    it('should handle complex vessel selection states', () => {
      render(
        <VesselDropdownMenu 
          {...defaultProps} 
          selectedVessels={['Vessel A', 'Vessel D']}
          isAllSelected={false}
        />
      );
      
      const checkboxes = screen.getAllByRole('checkbox');
      
      // Check that correct vessels are selected
      const vesselACheckbox = checkboxes.find(cb => 
        cb.parentElement?.textContent?.includes('Vessel A')
      ) as HTMLInputElement;
      const vesselBCheckbox = checkboxes.find(cb => 
        cb.parentElement?.textContent?.includes('Vessel B')
      ) as HTMLInputElement;
      const vesselDCheckbox = checkboxes.find(cb => 
        cb.parentElement?.textContent?.includes('Vessel D')
      ) as HTMLInputElement;
      
      expect(vesselACheckbox?.checked).toBe(true);
      expect(vesselBCheckbox?.checked).toBe(false);
      expect(vesselDCheckbox?.checked).toBe(true);
    });

    it('should handle search with filtered results', () => {
      const filteredGroups: VesselGroup[] = [
        {
          id: 1,
          title: 'Filtered Group',
          vessels: [
            { vessel_id: 1, name: 'Vessel A', vessel_ownership_id: 101 },
          ],
        },
      ];
      
      render(
        <VesselDropdownMenu 
          {...defaultProps} 
          searchTerm="Vessel A"
          filteredGroups={filteredGroups}
        />
      );
      
      expect(screen.getByText('Vessel A')).toBeInTheDocument();
      expect(screen.queryByText('Vessel B')).not.toBeInTheDocument();
      expect(screen.queryByText('Vessel C')).not.toBeInTheDocument();
      expect(screen.queryByText('Vessel D')).not.toBeInTheDocument();
    });
  });
});
