import React from 'react';
import { MailI<PERSON>, Arrow<PERSON><PERSON>, ArrowDown, ChevronsUpDown } from 'lucide-react';
import classNames from 'classnames';
import { SortConfig, Vessel, VesselTableProps } from '../../../types/types';
import { useInfiniteScroll } from '../../../hooks/useInfiniteScroll';
import Spinner from './Spinner';
import styles from '../styles/vessel-widget-scss/VesselTable.module.scss';
import { ExternalLinkIcon } from './svgIcons';

function hexToRgb(hex: string): string {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}`
    : '128, 128, 128';
}

// New sub-components to be added to your file
const TruncatedCell: React.FC<{ value: string | number }> = ({ value }) => {
  const str = value.toString();
  const truncated = str.length > 15 ? `${str.slice(0, 15)}...` : str;
  return (
    <span className={styles['ra-vesselSecondColumnEntry']} title={str}>
      {truncated}
    </span>
  );
};

const StatusBadgeCell: React.FC<{ value: string | number; badgeColors: string[] }> = ({
  value,
  badgeColors,
}) => {
  const statusColors: { [key: string]: string } = {
    Critical: badgeColors[0] || '#f44336',
    Special: badgeColors[1] || '#fbc02d',
    Unassigned: badgeColors[2] || '#808080',
  };
  const selectedColor = statusColors[value as string] || '#808080';
  const cellStyle = { '--status-rgb': hexToRgb(selectedColor) } as React.CSSProperties;
  return (
    <span className={styles['ra-statusBadge']} style={cellStyle}>
      {value.toString()}
    </span>
  );
};

const ApprovalStatusCell: React.FC<{ value: string | number; badgeColors: string[] }> = ({
  value,
  badgeColors,
}) => {
  const statusMap: { [key: string]: string } = {
    Approved: styles['ra-approvedBadge'],
    'Approved with Condition': styles['ra-approvedWithConditionBadge'],
    Rejected: styles['ra-rejectedBadge'],
  };
  const badgeClass = statusMap[value as string];
  const badgeClasses = classNames(styles['ra-statusBadge'], badgeClass);

  let color: string | undefined;
  if (value === 'Pending') {
    color = badgeColors[1] || '#fbc02d';
  } else if (!badgeClass) {
    color = '#808080';
  }

  const cellStyle = color ? ({ '--status-rgb': hexToRgb(color) } as React.CSSProperties) : {};

  return (
    <span className={badgeClasses} style={cellStyle}>
      {value.toString()}
    </span>
  );
};

const DataCellContent: React.FC<{
  cellStyleType: 'default' | 'conditional';
  columnIndex: number;
  value: string | number;
  badgeColors: string[];
}> = React.memo(({ cellStyleType, columnIndex, value, badgeColors }) => {
  if (cellStyleType === 'conditional') {
    switch (columnIndex) {
      case 0:
        return <TruncatedCell value={value} />;
      case 1:
        return <StatusBadgeCell value={value} badgeColors={badgeColors} />;
      case 2:
        return <ApprovalStatusCell value={value} badgeColors={badgeColors} />;
      default:
        return null;
    }
  }

  // This part remains the same, as it's already simple
  const defaultBadgeColor = badgeColors[columnIndex] || '#808080';
  const badgeStyle = {
    '--badge-bg-color': defaultBadgeColor,
    '--badge-text-color': ['#fbc02d', '#ffeb3b'].includes(defaultBadgeColor.toLowerCase())
      ? 'black'
      : 'white',
  } as React.CSSProperties;

  return (
    <span className={styles['ra-badge']} style={badgeStyle}>
      {value.toString()}
    </span>
  );
});

export const getSortIcon = (headerKey: string, sortConfig: SortConfig) => {
    if (!sortConfig || sortConfig.key !== headerKey) {
      return (
        <ChevronsUpDown size={14} className={classNames(styles['ra-sortIcon'], styles.neutral)} />
      );
    }
    return sortConfig.direction === 'ascending' ? (
      <ArrowUp size={14} className={classNames(styles['ra-sortIcon'], styles.active)} />
    ) : (
      <ArrowDown size={14} className={classNames(styles['ra-sortIcon'], styles.active)} />
    );
  };

const VesselTableHeader: React.FC<{
  headers: string[];
  sortConfig: SortConfig;
  onSort: (key: string) => void;
}> = React.memo(({ headers, sortConfig, onSort }) => {


  return (
    <thead className={styles['ra-tableHeader']}>
      <tr>
        {headers.map((header) => (
          <th
            key={header} // Use the unique header name as the key
            className={classNames({ [styles['ra-textLeft']]: headers.indexOf(header) === 0 })}
            onClick={() => onSort(header)}
          >
            <div className={styles['ra-headerContent']}>
              {header}
            </div>
          </th>
        ))}
        <th key="actions" className={classNames(styles['ra-actionHeader'], styles.nonSortable)}>
          <div className={styles['ra-headerContent']}>Actions</div>
        </th>
      </tr>
    </thead>
  );
});

// --- Sub-component: Table Row (FIXED) ---
const VesselTableRow: React.FC<{
  vessel: Vessel;
  badgeColors: string[];
  onSendEmail: (vessel: Vessel) => void;
  onVesselClick: (vessel: Vessel) => void;
  cellStyleType: 'default' | 'conditional';
}> = React.memo(({ vessel, badgeColors, onSendEmail, onVesselClick, cellStyleType }) => (
  <tr className={styles['ra-tableRow']}>
    {/* Column 1: Vessel Name is rendered separately first */}
    <td className={styles['ra-vesselNameCell']}>
      <button onClick={() => onVesselClick(vessel)} className={styles['ra-vesselNameButton']}>
        {vessel.name}
      </button>
    </td>

    {/* Subsequent columns are mapped from the vesselData array */}
    {vessel.vesselData.map((value, i) => {
      return (
      <td key={`${vessel.vessel_id}-${vessel.vessel_ownership_id}`}>
        <DataCellContent
          cellStyleType={cellStyleType}
          columnIndex={i}
          value={value}
          badgeColors={badgeColors}
        />
      </td>
    )
    })}

    {/* Final Column: Actions */}
    <td className={styles['ra-emailCell']}>
      <div className={styles['ra-emailButtonWrapper']}>
        <button onClick={() => onSendEmail(vessel)} className={styles['ra-emailButton']}>
          {cellStyleType === 'conditional' ? <ExternalLinkIcon /> : <MailIcon size={20} />}
        </button>
        <div className={styles['ra-tooltip']}>
          {cellStyleType === 'conditional' ? 'View Details' : 'Send Email'}
        </div>
      </div>
    </td>
  </tr>
));

// --- Main Component ---
export default function VesselTable({
  vessels,
  tableHeaders,
  badgeColors,
  onSendEmail,
  onVesselClick,
  isFetchingNextPage,
  isLoading,
  fetchNextPage,
  pagination,
  cellStyleType = 'default',
  sortConfig,
  onSort,
}: Readonly<VesselTableProps>) {
  const hasNextPage = pagination && pagination.page < pagination.totalPages;
  const dataHeaders = tableHeaders.filter((h) => h.toLowerCase() !== 'action');
  const { containerRef, handleScroll } = useInfiniteScroll({
    fetchNextPage,
    isFetchingNextPage,
    hasNextPage: !!hasNextPage,
    dataLength: vessels?.length || 0,
  });

  const renderBody = () => {
    if (isLoading) {
      return (
        <tr key="loading">
          <td colSpan={dataHeaders.length + 1} className={styles['ra-statusCell']}>
            <Spinner />
          </td>
        </tr>
      );
    }
    if (!vessels || vessels.length === 0) {
      return (
        <tr key="no-results">
          <td colSpan={dataHeaders.length + 1} className={styles['ra-statusCell']}>
            No results found
          </td>
        </tr>
      );
    }
    return vessels.map((vessel) => (
      <VesselTableRow
        key={vessel.vessel_id}
        vessel={vessel}
        badgeColors={badgeColors}
        onSendEmail={onSendEmail}
        onVesselClick={onVesselClick}
        cellStyleType={cellStyleType}
      />
    ));
  };

  return (
    <div ref={containerRef} onScroll={handleScroll} className={styles['ra-tableContainer']}>
      <table className={styles['ra-table']}>
        <VesselTableHeader headers={dataHeaders} sortConfig={sortConfig} onSort={onSort} />
        <tbody>{renderBody()}</tbody>
      </table>
      {isFetchingNextPage && (
        <div className={styles['ra-spinnerContainer']}>
          <Spinner />
        </div>
      )}
    </div>
  );
}
